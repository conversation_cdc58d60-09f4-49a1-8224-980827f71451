<?php $__env->startSection('title', 'Product Management'); ?>

<?php $__env->startSection('content'); ?>
<div class="d-flex justify-content-between align-items-center mb-4">
    <h2><i class="fas fa-box me-2"></i>Product Management</h2>
    <div class="btn-group">
        <a href="<?php echo e(route('admin.products.index')); ?>" class="btn btn-outline-primary <?php echo e(!request('status') ? 'active' : ''); ?>">All Products</a>
        <a href="<?php echo e(route('admin.products.index', ['status' => 'active'])); ?>" class="btn btn-outline-success <?php echo e(request('status') == 'active' ? 'active' : ''); ?>">Active</a>
        <a href="<?php echo e(route('admin.products.index', ['status' => 'pending'])); ?>" class="btn btn-outline-warning <?php echo e(request('status') == 'pending' ? 'active' : ''); ?>">Pending</a>
        <a href="<?php echo e(route('admin.products.index', ['status' => 'inactive'])); ?>" class="btn btn-outline-secondary <?php echo e(request('status') == 'inactive' ? 'active' : ''); ?>">Inactive</a>
    </div>
</div>

<!-- Statistics Cards -->
<div class="row mb-4">
    <div class="col-md-2 mb-3">
        <div class="card stat-card text-center">
            <div class="card-body">
                <div class="text-primary mb-2">
                    <i class="fas fa-box fa-2x"></i>
                </div>
                <h3 class="mb-0"><?php echo e($stats['total_products']); ?></h3>
                <small class="text-muted">Total Products</small>
            </div>
        </div>
    </div>

    <div class="col-md-2 mb-3">
        <div class="card stat-card text-center">
            <div class="card-body">
                <div class="text-success mb-2">
                    <i class="fas fa-check-circle fa-2x"></i>
                </div>
                <h3 class="mb-0"><?php echo e($stats['active_products']); ?></h3>
                <small class="text-muted">Active</small>
            </div>
        </div>
    </div>

    <div class="col-md-2 mb-3">
        <div class="card stat-card text-center">
            <div class="card-body">
                <div class="text-warning mb-2">
                    <i class="fas fa-clock fa-2x"></i>
                </div>
                <h3 class="mb-0"><?php echo e($stats['pending_products']); ?></h3>
                <small class="text-muted">Pending</small>
            </div>
        </div>
    </div>

    <div class="col-md-2 mb-3">
        <div class="card stat-card text-center">
            <div class="card-body">
                <div class="text-secondary mb-2">
                    <i class="fas fa-pause-circle fa-2x"></i>
                </div>
                <h3 class="mb-0"><?php echo e($stats['inactive_products']); ?></h3>
                <small class="text-muted">Inactive</small>
            </div>
        </div>
    </div>

    <div class="col-md-2 mb-3">
        <div class="card stat-card text-center">
            <div class="card-body">
                <div class="text-danger mb-2">
                    <i class="fas fa-exclamation-triangle fa-2x"></i>
                </div>
                <h3 class="mb-0"><?php echo e($stats['low_stock_products']); ?></h3>
                <small class="text-muted">Low Stock</small>
            </div>
        </div>
    </div>
</div>

<!-- Search and Filters -->
<div class="card mb-4">
    <div class="card-body">
        <form method="GET" action="<?php echo e(route('admin.products.index')); ?>" class="row g-3">
            <div class="col-md-4">
                <label for="search" class="form-label">Search Products</label>
                <input type="text" class="form-control" id="search" name="search"
                       value="<?php echo e(request('search')); ?>" placeholder="Product name, seller, category...">
            </div>
            <div class="col-md-2">
                <label for="status" class="form-label">Status</label>
                <select class="form-select" id="status" name="status">
                    <option value="">All Statuses</option>
                    <option value="active" <?php echo e(request('status') == 'active' ? 'selected' : ''); ?>>Active</option>
                    <option value="pending" <?php echo e(request('status') == 'pending' ? 'selected' : ''); ?>>Pending</option>
                    <option value="inactive" <?php echo e(request('status') == 'inactive' ? 'selected' : ''); ?>>Inactive</option>
                    <option value="rejected" <?php echo e(request('status') == 'rejected' ? 'selected' : ''); ?>>Rejected</option>
                </select>
            </div>
            <div class="col-md-2">
                <label for="category" class="form-label">Category</label>
                <select class="form-select" id="category" name="category">
                    <option value="">All Categories</option>
                    <?php $__currentLoopData = $categories; $__env->addLoop($__currentLoopData); foreach($__currentLoopData as $category): $__env->incrementLoopIndices(); $loop = $__env->getLastLoop(); ?>
                        <option value="<?php echo e($category); ?>" <?php echo e(request('category') == $category ? 'selected' : ''); ?>>
                            <?php echo e($category); ?>

                        </option>
                    <?php endforeach; $__env->popLoop(); $loop = $__env->getLastLoop(); ?>
                </select>
            </div>
            <div class="col-md-2">
                <label for="sort" class="form-label">Sort By</label>
                <select class="form-select" id="sort" name="sort">
                    <option value="created_at">Newest First</option>
                    <option value="name">Name A-Z</option>
                    <option value="price_low">Price Low-High</option>
                    <option value="price_high">Price High-Low</option>
                </select>
            </div>
            <div class="col-md-2 d-flex align-items-end">
                <button type="submit" class="btn btn-primary me-2">
                    <i class="fas fa-search"></i> Search
                </button>
                <a href="<?php echo e(route('admin.products.index')); ?>" class="btn btn-outline-secondary">
                    <i class="fas fa-times"></i> Clear
                </a>
            </div>
        </form>
    </div>
</div>

<!-- Products Grid -->
<div class="row">
    <?php $__empty_1 = true; $__currentLoopData = $products; $__env->addLoop($__currentLoopData); foreach($__currentLoopData as $product): $__env->incrementLoopIndices(); $loop = $__env->getLastLoop(); $__empty_1 = false; ?>
        <div class="col-md-6 col-lg-4 mb-4">
            <div class="card product-card h-100">
                <div class="position-relative">
                    <img src="<?php echo e($product->image); ?>" class="card-img-top" alt="<?php echo e($product->name); ?>" style="height: 200px; object-fit: cover;">
                    <div class="position-absolute top-0 end-0 m-2">
                        <?php
                            $statusClasses = [
                                'active' => 'success',
                                'pending' => 'warning',
                                'inactive' => 'secondary',
                                'rejected' => 'danger'
                            ];
                        ?>
                        <span class="badge bg-<?php echo e($statusClasses[$product->status] ?? 'secondary'); ?>">
                            <?php echo e(ucfirst($product->status)); ?>

                        </span>
                    </div>
                    <?php if($product->stock <= 5): ?>
                        <div class="position-absolute top-0 start-0 m-2">
                            <span class="badge bg-danger">Low Stock</span>
                        </div>
                    <?php endif; ?>
                </div>

                <div class="card-body d-flex flex-column">
                    <h6 class="card-title"><?php echo e($product->name); ?></h6>
                    <p class="text-muted small mb-2">
                        <i class="fas fa-store me-1"></i><?php echo e($product->seller_name); ?>

                    </p>
                    <p class="text-muted small mb-2">
                        <i class="fas fa-tag me-1"></i><?php echo e($product->category); ?>

                    </p>

                    <div class="mt-auto">
                        <div class="d-flex justify-content-between align-items-center mb-3">
                            <div>
                                <h5 class="text-success mb-0">$<?php echo e(number_format($product->price, 2)); ?></h5>
                                <small class="text-muted">Stock: <?php echo e($product->stock); ?></small>
                            </div>
                        </div>

                        <div class="btn-group w-100">
                            <a href="<?php echo e(route('admin.products.show', $product->id)); ?>"
                               class="btn btn-outline-primary btn-sm">
                                <i class="fas fa-eye"></i> View
                            </a>

                            <?php if($product->status == 'pending'): ?>
                                <button type="button" class="btn btn-outline-success btn-sm"
                                        onclick="approveProduct(<?php echo e($product->id); ?>)">
                                    <i class="fas fa-check"></i> Approve
                                </button>
                                <button type="button" class="btn btn-outline-danger btn-sm"
                                        onclick="rejectProduct(<?php echo e($product->id); ?>)">
                                    <i class="fas fa-times"></i> Reject
                                </button>
                            <?php else: ?>
                                <div class="dropdown">
                                    <button class="btn btn-outline-secondary btn-sm dropdown-toggle"
                                            type="button" data-bs-toggle="dropdown">
                                        <i class="fas fa-cog"></i> Actions
                                    </button>
                                    <ul class="dropdown-menu">
                                        <?php if($product->status == 'active'): ?>
                                            <li><a class="dropdown-item" href="#" onclick="updateStatus(<?php echo e($product->id); ?>, 'inactive')">
                                                <i class="fas fa-pause me-2"></i>Deactivate
                                            </a></li>
                                        <?php else: ?>
                                            <li><a class="dropdown-item" href="#" onclick="updateStatus(<?php echo e($product->id); ?>, 'active')">
                                                <i class="fas fa-play me-2"></i>Activate
                                            </a></li>
                                        <?php endif; ?>
                                        <li><hr class="dropdown-divider"></li>
                                        <li><a class="dropdown-item text-danger" href="#" onclick="deleteProduct(<?php echo e($product->id); ?>)">
                                            <i class="fas fa-trash me-2"></i>Delete
                                        </a></li>
                                    </ul>
                                </div>
                            <?php endif; ?>
                        </div>
                    </div>
                </div>
            </div>
        </div>
    <?php endforeach; $__env->popLoop(); $loop = $__env->getLastLoop(); if ($__empty_1): ?>
        <div class="col-12">
            <div class="text-center py-5">
                <i class="fas fa-box fa-4x text-muted mb-3"></i>
                <h4 class="text-muted">No Products Found</h4>
                <p class="text-muted">No products match your current search criteria.</p>
                <a href="<?php echo e(route('admin.products.index')); ?>" class="btn btn-primary">
                    <i class="fas fa-redo me-1"></i>Clear Filters
                </a>
            </div>
        </div>
    <?php endif; ?>
</div>

<!-- Bulk Actions Modal -->
<div class="modal fade" id="bulkActionsModal" tabindex="-1">
    <div class="modal-dialog">
        <div class="modal-content">
            <div class="modal-header">
                <h5 class="modal-title">Bulk Actions</h5>
                <button type="button" class="btn-close" data-bs-dismiss="modal"></button>
            </div>
            <div class="modal-body">
                <p>Select an action to perform on selected products:</p>
                <div class="d-grid gap-2">
                    <button type="button" class="btn btn-success" onclick="bulkApprove()">
                        <i class="fas fa-check me-2"></i>Approve Selected
                    </button>
                    <button type="button" class="btn btn-warning" onclick="bulkDeactivate()">
                        <i class="fas fa-pause me-2"></i>Deactivate Selected
                    </button>
                    <button type="button" class="btn btn-danger" onclick="bulkDelete()">
                        <i class="fas fa-trash me-2"></i>Delete Selected
                    </button>
                </div>
            </div>
        </div>
    </div>
</div>
<?php $__env->stopSection(); ?>

<?php $__env->startPush('styles'); ?>
<style>
.product-card {
    transition: transform 0.2s;
}

.product-card:hover {
    transform: translateY(-2px);
    box-shadow: 0 4px 8px rgba(0,0,0,0.1);
}

.stat-card {
    transition: transform 0.2s;
}

.stat-card:hover {
    transform: translateY(-2px);
}
</style>
<?php $__env->stopPush(); ?>

<?php $__env->startPush('scripts'); ?>
<script>
function approveProduct(productId) {
    if (confirm('Are you sure you want to approve this product?')) {
        // In a real implementation, make AJAX call to approve
        console.log('Approving product:', productId);
        alert('Product would be approved');
    }
}

function rejectProduct(productId) {
    const reason = prompt('Please provide a reason for rejection:');
    if (reason) {
        // In a real implementation, make AJAX call to reject with reason
        console.log('Rejecting product:', productId, 'Reason:', reason);
        alert('Product would be rejected with reason: ' + reason);
    }
}

function updateStatus(productId, status) {
    if (confirm('Are you sure you want to update this product status?')) {
        // In a real implementation, make AJAX call to update status
        console.log('Updating product:', productId, 'to status:', status);
        alert('Product status would be updated to: ' + status);
    }
}

function deleteProduct(productId) {
    if (confirm('Are you sure you want to delete this product? This action cannot be undone.')) {
        // In a real implementation, make AJAX call to delete
        console.log('Deleting product:', productId);
        alert('Product would be deleted');
    }
}
</script>
<?php $__env->stopPush(); ?>

<?php echo $__env->make('admin.layout', array_diff_key(get_defined_vars(), ['__data' => 1, '__path' => 1]))->render(); ?><?php /**PATH C:\xampp11\htdocs\bll\resources\views/admin/products/index.blade.php ENDPATH**/ ?>